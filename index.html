<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Dynamics - Software Development Company</title>

    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="icons/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/favicon-16x16.png">
    <link rel="manifest" href="icons/site.webmanifest">
    <link rel="shortcut icon" href="icons/favicon.ico">

    <link rel="stylesheet" href="styles.css?v=102">
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/cd_logo.png" alt="Code Dynamics Logo" class="logo-img">
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#hero" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#testimonials" class="nav-link">Testimonials</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">Solutions That Scale.<br>Code That Lasts.</h1>
                <p class="hero-subtitle">Your vision. Our code. Scalable, tailored software - built to deliver.<br>We
                    are Code Dynamics!</p>
                <div class="hero-buttons">
                    <a href="#projects" class="btn btn-primary">View Our Work</a>
                    <a href="#contact" class="btn btn-secondary">Get In Touch</a>
                </div>
            </div>

        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">Solutions We’ve Delivered</h2>
            <div class="projects-showcase">
                <div class="projects-scroll-container">
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/respond_52.jpg" alt="Respond 52">
                    </div>
                    <div class="project-content">
                        <h3>Respond 52</h3>
                        <p>Field-ready mobile application for first responders with real-time data access.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Flutter</span>
                            <span class="tech-tag">Firebase</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/flight_vision.jpg" alt="Flight Vision">
                    </div>
                    <div class="project-content">
                        <h3>Flight Vision</h3>
                        <p>A fully customisable airport flight information display solution (FIDS).</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Azure SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/brokers_edge.jpg" alt="Brokers Edge">
                    </div>
                    <div class="project-content">
                        <h3>Broker's Edge</h3>
                        <p>Finance Broker's client & loans management software.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Microsoft SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/rt_mobile.jpg" alt="RT Mobile App">
                    </div>
                    <div class="project-content">
                        <h3>RT Mobile App</h3>
                        <p>Tow Truck job & driver management software.</p>
                        <div class="project-tech">
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/order_numbers.jpg" alt="Order Numbers">
                    </div>
                    <div class="project-content">
                        <h3>Order Numbers</h3>
                        <p>Customer order number queue display for takeaway & fast food outlets.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Azure SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/benchmark.jpg" alt="Benchmark">
                    </div>
                    <div class="project-content">
                        <h3>Benchmark</h3>
                        <p>Operational benchmarking software for food service venues.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Microsoft SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/capsules.jpg" alt="Capsules">
                    </div>
                    <div class="project-content">
                        <h3>Capsules</h3>
                        <p>Client & order management software for coffee pod manufacturing.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Microsoft SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/rodan_fields.jpg" alt="R+F Manager">
                    </div>
                    <div class="project-content">
                        <h3>R+F Manager</h3>
                        <p>Client & order management app for Rodan & Fields business consultants.</p>
                        <div class="project-tech">
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/bluefrog_pos.jpg" alt="Bluefrog POS">
                    </div>
                    <div class="project-content">
                        <h3>Bluefrog POS</h3>
                        <p>Point of Sale software solution for cafés, restaurants, pizza bars & fast food outlets.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Microsoft SQL</span>
                        </div>
                    </div>
                </div>

                <!-- Duplicate cards for seamless loop -->
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/respond_52.jpg" alt="Respond 52">
                    </div>
                    <div class="project-content">
                        <h3>Respond 52</h3>
                        <p>Field-ready mobile application for first responders with real-time data access.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Flutter</span>
                            <span class="tech-tag">Firebase</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/flight_vision.jpg" alt="Flight Vision">
                    </div>
                    <div class="project-content">
                        <h3>Flight Vision</h3>
                        <p>A fully customisable airport flight information display solution (FIDS).</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Azure SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/brokers_edge.jpg" alt="Brokers Edge">
                    </div>
                    <div class="project-content">
                        <h3>Broker's Edge</h3>
                        <p>Finance Broker's client & loans management software.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Microsoft SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/rt_mobile.jpg" alt="RT Mobile App">
                    </div>
                    <div class="project-content">
                        <h3>RT Mobile App</h3>
                        <p>Tow Truck job & driver management software.</p>
                        <div class="project-tech">
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/order_numbers.jpg" alt="Order Numbers">
                    </div>
                    <div class="project-content">
                        <h3>Order Numbers</h3>
                        <p>Customer order number queue display for takeaway & fast food outlets.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Azure SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/benchmark.jpg" alt="Benchmark">
                    </div>
                    <div class="project-content">
                        <h3>Benchmark</h3>
                        <p>Operational benchmarking software for food service venues.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Microsoft SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/capsules.jpg" alt="Capsules">
                    </div>
                    <div class="project-content">
                        <h3>Capsules</h3>
                        <p>Client & order management software for coffee pod manufacturing.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Microsoft SQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/rodan_fields.jpg" alt="R+F Manager">
                    </div>
                    <div class="project-content">
                        <h3>R+F Manager</h3>
                        <p>Client & order management app for Rodan & Fields business consultants.</p>
                        <div class="project-tech">
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">MySQL</span>
                        </div>
                    </div>
                </div>
                <div class="project-card">
                    <div class="project-image">
                        <img src="images/bluefrog_pos.jpg" alt="Bluefrog POS">
                    </div>
                    <div class="project-content">
                        <h3>Bluefrog POS</h3>
                        <p>Point of Sale software solution for cafés, restaurants, pizza bars & fast food outlets.</p>
                        <div class="project-tech">
                            <span class="tech-tag">Visual Basic</span>
                            <span class="tech-tag">Microsoft SQL</span>
                        </div>
                    </div>
                </div>
                </div>
                <div class="showcase-controls">
                    <button id="playPauseBtn" class="play-pause-btn">
                        <span class="pause-icon">⏸️</span>
                        <span class="play-icon" style="display: none;">▶️</span>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div id="particles-js-about"></div>
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2 class="section-title">About Code Dynamics</h2>
                    <p>Founded in 2016, Code Dynamics specialises in fast, high-quality software tailored to your
                        business needs. We turn ideas into robust, scalable solutions—on time and on point.</p>
                    <div class="about-stats">
                        <div class="stat">
                            <h3>35+</h3>
                            <p>Projects Developed</p>
                        </div>
                        <div class="stat">
                            <h3>5,000+</h3>
                            <p>Active Users</p>
                        </div>
                        <div class="stat">
                            <h3>25+</h3>
                            <p>Years Experience</p>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <div class="team-illustration">
                        <img src="images/developers.jpg" alt="Code Dynamics Team" class="team-photo">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="testimonials">
        <div class="container">
            <h2 class="section-title">What Our Clients Say</h2>
            <div class="testimonials-slider">
                <div class="testimonial active">
                    <div class="testimonial-content">
                        <p>"As a specialised business we initially tried to make an “off the shelf” accounting package
                            work for us but quickly realised that we needed a custom solution. I was recommended to Code
                            Dynamics and within a few months they had developed a custom software package to help us
                            manage our clients & orders."</p>
                        <div class="testimonial-author">
                            <h4>Paul Maione</h4>
                            <span>Owner, MAIPAC</span>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-content">
                        <p>"I liked the convenience of dealing with someone local and communicating with Code Dynamics
                            was painless. They understood what I was trying to achieve and I was able to be involved
                            every step of the way during development."</p>
                        <div class="testimonial-author">
                            <h4>Pius Steiner</h4>
                            <span>Owner, LIKUID Espresso</span>
                        </div>
                    </div>
                </div>
                <div class="testimonial">
                    <div class="testimonial-content">
                        <p>"Due to time constraints with a current software developer, we used Code Dynamics to design a
                            transitional solution. The attention to detail & speed with which they developed the package
                            was appreciated. They understood our needs & produced an outcome that was user friendly &
                            met the targets we had set. I have no hesitation in recommending Code Dynamics."</p>
                        <div class="testimonial-author">
                            <h4>David Smith</h4>
                            <span>Station Officer, SAMFS</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="testimonial-dots">
                <span class="dot active" data-slide="0"></span>
                <span class="dot" data-slide="1"></span>
                <span class="dot" data-slide="2"></span>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div id="particles-js"></div>
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <form class="contact-form" action="/php/contact_new.php" method="POST">
                    <div class="form-group">
                        <input type="text" id="name" name="name" placeholder="Your Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="email" name="email" placeholder="Your Email" required>
                    </div>
                    <div class="form-group">
                        <input type="phone" id="phone" name="phone" placeholder="Your Contact Number (optional)">
                    </div>
                    <div class="form-group">
                        <select id="department" name="department" required>
                            <option value="" disabled selected>Select Enquiry Type</option>
                            <option value="sales">Sales</option>
                            <option value="accounts">Accounts</option>
                            <option value="support">Support</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                    </div>
                    <div class="form-group recaptcha-container">
                        <div class="g-recaptcha" data-sitekey="6LfXF34rAAAAADrrRzkxdS3xC4ZaDQ5nRSfVJYdi"
                            data-theme="dark" data-callback="enableSubmitButton"></div>
                    </div>
                    <button type="submit" id="submit-button" class="btn btn-primary" disabled>Send Message</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="images/cd_logo.png" alt="Code Dynamics Logo" class="footer-logo-img">
                    <p><i>Solutions That Scale. Code That Lasts.</i></p>
                </div>
                <div class="footer-social">
                    <a href="https://www.linkedin.com/company/18087000/admin/dashboard/" target="_blank"><i
                            class="fab fa-linkedin"></i></a>
                    <a href="https://github.com/codedynamics" target="_blank"><i class="fab fa-github"></i></a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 <strong>Code Dynamics Pty Ltd.</strong> All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js?v=102"></script>
</body>

</html>